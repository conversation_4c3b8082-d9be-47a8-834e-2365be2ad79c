import 'package:cussme/domain/domain.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class WordListItem extends StatelessWidget {
  final WordEntity word;
  final VoidCallback onTap;
  final VoidCallback? onBookmarkTap;
  final VoidCallback onPlayPronunciation;

  final bool showBookmark;

  const WordListItem({
    super.key,
    required this.word,
    required this.onTap,
    required this.onPlayPronunciation,
    this.onBookmarkTap,
    this.showBookmark = true,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        highlightColor: Palette.whiteEb,
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Palette.outlineVariant, width: 1),
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Flexible(
                                child: Container(
                                  padding: EdgeInsets.zero,
                                  margin: EdgeInsets.zero,
                                  child: Text(
                                    word.word,
                                    style: TextStyles.titleSmall.copyWith(
                                      color: Palette.black1d,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              SvgPicture.asset(
                                word.spiciness.getIconPath(),
                                width: 16,
                                height: 16,
                                colorFilter: const ColorFilter.mode(
                                  Palette.onSurface,
                                  BlendMode.srcIn,
                                ),
                              ),
                              const SizedBox(width: 16),
                            ],
                          ),
                        ),
                      ],
                    ),
                    if (word.phonetic != null) ...[
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Flexible(
                                  child: Container(
                                    padding: EdgeInsets.zero,
                                    margin: EdgeInsets.zero,
                                    child: Text(
                                      word.phonetic!,
                                      style: TextStyles.bodyMedium.copyWith(
                                        color: Palette.onSurface,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ),
                                SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: IconButton(
                                    padding: EdgeInsets.zero,
                                    constraints: const BoxConstraints(),
                                    icon: const Icon(Icons.volume_up_outlined,
                                        size: 16),
                                    color: Palette.purpule4f,
                                    onPressed: onPlayPronunciation,
                                  ),
                                ),
                                const SizedBox(width: 16),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                    Text(
                      word.meaning,
                      style: TextStyles.bodyMedium.copyWith(
                        color: Palette.onSurface,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              if (showBookmark)
                SizedBox(
                  width: 24,
                  height: 24,
                  child: IconButton(
                    padding: EdgeInsets.zero,
                    icon: Icon(
                      word.isBookmarked
                          ? Icons.bookmark
                          : Icons.bookmark_border,
                    ),
                    color: Palette.primary,
                    onPressed: onBookmarkTap,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
