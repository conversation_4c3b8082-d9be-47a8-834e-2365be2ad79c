import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class WordListItem extends StatelessWidget {
  final WordEntity word;
  final VoidCallback onTap;
  final VoidCallback? onBookmarkTap;
  final VoidCallback onPlayPronunciation;

  final bool showBookmark;

  const WordListItem({
    super.key,
    required this.word,
    required this.onTap,
    required this.onPlayPronunciation,
    this.onBookmarkTap,
    this.showBookmark = true,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        highlightColor: Palette.whiteEb,
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Palette.borderLight, width: 1),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  SvgPicture.asset(
                    word.spiciness.getIconPath(),
                    width: 12,
                    height: 12,
                    colorFilter: const ColorFilter.mode(
                      Palette.onSurfaceVariant,
                      BlendMode.srcIn,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    word.spiciness.getTitle().toUpperCase(),
                    style: TextStyles.labelSmall
                        .copyWith(color: Palette.onSurfaceVariant),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                word.word,
                                style: TextStyles.titleMedium.copyWith(
                                  color: Palette.black1d,
                                  fontWeight: FontWeight.w600,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(width: 12),
                            if (showBookmark)
                              SizedBox(
                                width: 24,
                                height: 24,
                                child: IconButton(
                                  padding: EdgeInsets.zero,
                                  constraints: const BoxConstraints(),
                                  icon: Icon(
                                    word.isBookmarked
                                        ? Icons.bookmark
                                        : Icons.bookmark_border,
                                    size: 24,
                                  ),
                                  color: word.isBookmarked
                                      ? Palette.primary
                                      : Palette.onSurfaceVariant,
                                  onPressed: onBookmarkTap,
                                ),
                              ),
                          ],
                        ),
                        if (word.phonetic != null) ...[
                          const SizedBox(height: 2),
                          Row(
                            children: [
                              InkWell(
                                onTap: onPlayPronunciation,
                                borderRadius: BorderRadius.circular(4),
                                child: Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 2),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      ConstrainedBox(
                                        constraints: const BoxConstraints(
                                          maxWidth:
                                              200, // Adjust this value as needed
                                        ),
                                        child: Text(
                                          word.phonetic!,
                                          style: TextStyles.bodyMedium.copyWith(
                                            color: Palette.onSurfaceVariant,
                                            fontStyle: FontStyle.italic,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      const Icon(
                                        Icons.volume_up_outlined,
                                        size: 16,
                                        color: Palette.purpule4f,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 2),
              Row(
                children: [
                  Text(
                    Str.of(context).meaning,
                    style: TextStyles.bodyMedium.copyWith(
                      color: Palette.black1d,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      word.meaning,
                      style: TextStyles.bodyLarge.copyWith(
                        color: Palette.onSurface,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
