import 'package:flutter/material.dart';

/// Color palette for the app based on Material 3 design system
class Palette {
  const Palette._();

  // Primary colors
  static const Color primary = Color(0xFFE61E25);
  static const Color primaryDark = Color(0xFF2B1919);
  static const Color primaryContainer = Color(0xFFFFFEEB);
  static const Color onPrimary = Colors.white;
  static const Color inversePrimary = Color(0xFFFFBCBC);

  // Secondary colors
  static const Color secondary = Color(0xFFF2632B);
  static const Color secondaryLight = Color(0x11F2632B);

  // Surface colors
  static const Color surface = Colors.white;
  static const Color onSurface = Color(0xFF49454F);
  static const Color inverseSurface = Color(0xFF322F35);
  static const Color inverseOnSurface = Color(0xFFF5EFF7);
  static const Color surfaceVariant = Color(0xFFF1F0F0);
  static const Color onSurfaceVariant = Color(0xFF79747E);
  static const Color onSecondaryFixedVariant = Color(0xFF584444);
  static const Color disabledButtonBackground = Color(0x1F1D1B20);

  // Outline colors
  static const Color outline = Color(0xFF79747E);
  static const Color outlineVariant = Color(0xFFCAC4D0);

  // Status colors
  static const Color error = Color(0xFFB3261E);
  static const Color success = Color(0xFF4CAF50);

  // Social colors
  static const Color google = Color(0xFF4285F4);
  static const Color facebook = Color(0xFF1877F2);

  // NonTheme colors
  static const Color whiteFa = Color(0xFFFAFAFA);
  static const Color whiteD3 = Color(0xFFFFFFD3);
  static const Color whiteEb = Color(0xFFFFFEEB);
  static const Color black1d = Color(0xFF1D1B20);
  static const Color yellowF7 = Color(0xFFF7EC26);
  static const Color purpule4f = Color(0xFF4F378B);
  static const Color black59 = Color(0xFF594444);
}
