import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class WordOfTheDayCard extends StatelessWidget {
  final WordEntity word;
  final bool isActive;
  final VoidCallback? onPlayPronunciation;
  final VoidCallback? onTap;

  const WordOfTheDayCard({
    super.key,
    required this.word,
    this.isActive = false,
    this.onPlayPronunciation,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final languageName = word.language.name;

    final backgroundColor = isActive ? Palette.primary : Palette.surface;
    final textColor = isActive ? Palette.surface : Palette.black1d;
    final secondaryTextColor = isActive ? Palette.surface : Palette.onSurface;
    final iconColor = isActive ? Palette.yellowF7 : Palette.primary;

    return SizedBox(
      width: 233,
      child: Transform.scale(
        scale: isActive ? 1.0 : 0.85, // Make inactive cards smaller
        child: GestureDetector(
          onTap: onTap,
          child: Card(
            margin: const EdgeInsets.only(top: 4.0, bottom: 4.0),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: isActive
                  ? BorderSide.none
                  : const BorderSide(color: Palette.outlineVariant),
            ),
            color: backgroundColor,
            elevation: 0,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    Str.of(context).wordOfTheDayFormat(languageName),
                    style: TextStyles.titleMedium.copyWith(color: textColor),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    word.word,
                    style: TextStyles.labelLarge.copyWith(
                      color: textColor,
                      fontWeight: isActive ? FontWeight.w400 : FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Row(
                    children: [
                      Text(
                        word.phonetic ?? '',
                        style: TextStyles.labelLarge.copyWith(
                            color: secondaryTextColor,
                            fontWeight: FontWeight.w400),
                      ),
                      const SizedBox(width: 10),
                      SizedBox(
                        width: 24,
                        height: 24,
                        child: IconButton(
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                          icon: const Icon(Icons.volume_up, size: 20),
                          color: iconColor,
                          onPressed: onPlayPronunciation,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 2),
                  Row(
                    children: [
                      SvgPicture.asset(
                        word.spiciness.getIconPath(),
                        width: 16,
                        height: 16,
                        colorFilter: ColorFilter.mode(
                          secondaryTextColor,
                          BlendMode.srcIn,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        word.spiciness.getTitle(),
                        style: TextStyles.labelMedium
                            .copyWith(color: secondaryTextColor),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Flexible(
                    child: Text(
                      word.meaning,
                      style: TextStyles.bodyMedium
                          .copyWith(color: secondaryTextColor),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
