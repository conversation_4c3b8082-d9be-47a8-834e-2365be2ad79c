# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# VS Code related
.vscode/*
.windsurf/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/snippets

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/
/coverage/
*.freezed.dart
*.g.dart
*.mocks.dart

# Web related
lib/generated_plugin_registrant.dart

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# iOS build artifacts
ios/Flutter/Flutter.framework/
ios/Flutter/App.framework/
ios/Flutter/Generated.xcconfig
ios/Flutter/flutter_export_environment.sh
ios/Flutter/ephemeral/
ios/Flutter/.last_build_id
ios/Flutter/app.flx
ios/Flutter/app.zip
ios/Flutter/flutter_assets/

# CocoaPods
ios/Pods/
ios/.symlinks/
ios/Podfile.lock

# Xcode user data
ios/Runner.xcworkspace/
ios/xcuserdata/
ios//*.xcuserstate
ios//*.xcworkspace
ios//*.xcodeproj/project.xcworkspace/
ios//*.xcodeproj/xcuserdata/

# Derived data
ios/DerivedData/
ios/build/
ios/**/build/

# Misc
ios/.generated/
ios/ServiceDefinitions.json
lib/localization/generated
