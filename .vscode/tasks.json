{
	"version": "2.0.0",
	"tasks": [
		{
			"type": "flutter",
			"command": "flutter",
			"args": [
				"pub",
				"run",
				"build_runner",
				"build",
				"--delete-conflicting-outputs"
			],
			"problemMatcher": [
				"$dart-build_runner"
			],
			"group": "build",
			"label": "flutter: flutter pub run build_runner build",
			"detail": ""
		},
		{
			"type": "dart",
			"command": "dart",
			"args": [
				"run",
				"intl_utils:generate",
			],
			"problemMatcher": [
				"$dart-build_runner"
			],
			"group": "build",
			"label": "dart run intl_utils:generate",
			"detail": ""
		}
	]
}